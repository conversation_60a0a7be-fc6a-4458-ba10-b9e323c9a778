# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment
.env
.env.local
.env.*.local
.env.production

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Uploads and reports
uploads/
reports/
*.pdf
*.html

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
build/

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
*.cover

# Temporary files
*.tmp
*.bak
*.cache

# Sensitive data
*.csv
*.xlsx
*.xls
!example_*.csv
!example_*.xlsx
