{"name": "infoblox-mcp-server", "version": "1.0.0", "description": "MCP server for InfoBlox WAPI integration", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "latest", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}