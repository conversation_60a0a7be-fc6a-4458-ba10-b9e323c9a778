# InfoBlox Configuration
INFOBLOX_GRID_MASTER=192.168.1.222
INFOBLOX_USERNAME=admin
INFOBLOX_PASSWORD=infoblox
INFOBLOX_NETWORK_VIEW=default
INFOBLOX_WAPI_VERSION=2.13.1
INFOBLOX_VERIFY_SSL=false

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_SECRET_KEY=your-secret-key-here

# Database (optional, for tracking imports)
DATABASE_URL=sqlite:///./infoblox_imports.db

# API Configuration
API_KEY=optional-api-key-for-web-interface
API_RATE_LIMIT=100

# File Upload
MAX_FILE_SIZE_MB=10
UPLOAD_DIRECTORY=./uploads
ALLOWED_EXTENSIONS=csv,xlsx,xls

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/infoblox_import.log

# EA Mapping
DEFAULT_EA_MAPPING_FILE=./config/ea_mappings.json

# Report Generation
REPORT_TEMPLATE_DIR=./templates
REPORT_OUTPUT_DIR=./reports

# Background Tasks (for web app)
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
